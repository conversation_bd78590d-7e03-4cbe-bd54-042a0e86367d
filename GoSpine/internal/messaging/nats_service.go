package messaging

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/nats-io/nats.go"
	"github.com/nats-io/nats.go/jetstream"
)

// 🚀 NATS Message Queue Service - High-Performance Event-Driven Architecture
// Optimized for HVAC transcription system with guaranteed delivery and scalability

// MessageType defines the type of message being processed
type MessageType string

const (
	MessageTypeEmailAttachment    MessageType = "email.attachment"
	MessageTypeTranscriptionJob   MessageType = "transcription.job"
	MessageTypeTranscriptionResult MessageType = "transcription.result"
	MessageTypeHVACAnalysis       MessageType = "hvac.analysis"
	MessageTypeSystemHealth       MessageType = "system.health"
)

// TranscriptionJobMessage represents a transcription job
type TranscriptionJobMessage struct {
	JobID        string            `json:"job_id"`
	EmailSource  string            `json:"email_source"`
	FileName     string            `json:"file_name"`
	AudioData    []byte            `json:"audio_data"`
	Metadata     map[string]string `json:"metadata"`
	Priority     int               `json:"priority"`
	CreatedAt    time.Time         `json:"created_at"`
	RetryCount   int               `json:"retry_count"`
}

// TranscriptionResultMessage represents transcription results
type TranscriptionResultMessage struct {
	JobID        string            `json:"job_id"`
	Text         string            `json:"text"`
	Confidence   float64           `json:"confidence"`
	Duration     time.Duration     `json:"duration"`
	HVACKeywords []string          `json:"hvac_keywords"`
	Metadata     map[string]string `json:"metadata"`
	ProcessedAt  time.Time         `json:"processed_at"`
	Success      bool              `json:"success"`
	Error        string            `json:"error,omitempty"`
}

// NATSConfig holds NATS configuration
type NATSConfig struct {
	URL                string        `yaml:"url"`
	ClusterID          string        `yaml:"cluster_id"`
	ClientID           string        `yaml:"client_id"`
	MaxReconnects      int           `yaml:"max_reconnects"`
	ReconnectWait      time.Duration `yaml:"reconnect_wait"`
	ConnectionTimeout  time.Duration `yaml:"connection_timeout"`
	StreamName         string        `yaml:"stream_name"`
	ConsumerGroup      string        `yaml:"consumer_group"`
	MaxAckPending      int           `yaml:"max_ack_pending"`
	AckWait            time.Duration `yaml:"ack_wait"`
}

// DefaultNATSConfig returns default NATS configuration
func DefaultNATSConfig() *NATSConfig {
	return &NATSConfig{
		URL:               "nats://localhost:4222",
		ClusterID:         "hvac-cluster",
		ClientID:          "hvac-transcription",
		MaxReconnects:     10,
		ReconnectWait:     2 * time.Second,
		ConnectionTimeout: 10 * time.Second,
		StreamName:        "HVAC_TRANSCRIPTION",
		ConsumerGroup:     "transcription-workers",
		MaxAckPending:     100,
		AckWait:           30 * time.Second,
	}
}

// NATSService manages NATS messaging for the HVAC system
type NATSService struct {
	conn       *nats.Conn
	js         jetstream.JetStream
	config     *NATSConfig
	logger     *log.Helper
	handlers   map[MessageType]MessageHandler
	consumers  map[string]jetstream.Consumer
	mu         sync.RWMutex
	ctx        context.Context
	cancel     context.CancelFunc
	wg         sync.WaitGroup
}

// MessageHandler defines the interface for message handlers
type MessageHandler interface {
	Handle(ctx context.Context, msg *nats.Msg) error
	MessageType() MessageType
}

// NewNATSService creates a new NATS service instance
func NewNATSService(config *NATSConfig, logger log.Logger) (*NATSService, error) {
	if config == nil {
		config = DefaultNATSConfig()
	}

	ctx, cancel := context.WithCancel(context.Background())

	service := &NATSService{
		config:    config,
		logger:    log.NewHelper(logger),
		handlers:  make(map[MessageType]MessageHandler),
		consumers: make(map[string]jetstream.Consumer),
		ctx:       ctx,
		cancel:    cancel,
	}

	if err := service.connect(); err != nil {
		cancel()
		return nil, fmt.Errorf("failed to connect to NATS: %w", err)
	}

	return service, nil
}

// connect establishes connection to NATS server
func (s *NATSService) connect() error {
	opts := []nats.Option{
		nats.Name(s.config.ClientID),
		nats.MaxReconnects(s.config.MaxReconnects),
		nats.ReconnectWait(s.config.ReconnectWait),
		nats.Timeout(s.config.ConnectionTimeout),
		nats.DisconnectErrHandler(func(nc *nats.Conn, err error) {
			s.logger.Warnf("NATS disconnected: %v", err)
		}),
		nats.ReconnectHandler(func(nc *nats.Conn) {
			s.logger.Info("NATS reconnected")
		}),
		nats.ClosedHandler(func(nc *nats.Conn) {
			s.logger.Info("NATS connection closed")
		}),
	}

	conn, err := nats.Connect(s.config.URL, opts...)
	if err != nil {
		return fmt.Errorf("failed to connect to NATS: %w", err)
	}

	js, err := jetstream.New(conn)
	if err != nil {
		conn.Close()
		return fmt.Errorf("failed to create JetStream context: %w", err)
	}

	s.conn = conn
	s.js = js

	// Create or update stream
	if err := s.setupStream(); err != nil {
		conn.Close()
		return fmt.Errorf("failed to setup stream: %w", err)
	}

	s.logger.Info("✅ NATS service connected successfully")
	return nil
}

// setupStream creates or updates the JetStream stream
func (s *NATSService) setupStream() error {
	streamConfig := jetstream.StreamConfig{
		Name:        s.config.StreamName,
		Description: "HVAC Transcription System Event Stream",
		Subjects:    []string{"hvac.>", "transcription.>", "email.>", "system.>"},
		Retention:   jetstream.WorkQueuePolicy,
		MaxAge:      24 * time.Hour,
		MaxBytes:    1024 * 1024 * 1024, // 1GB
		MaxMsgs:     1000000,
		Replicas:    1,
		Storage:     jetstream.FileStorage,
	}

	_, err := s.js.CreateOrUpdateStream(s.ctx, streamConfig)
	if err != nil {
		return fmt.Errorf("failed to create/update stream: %w", err)
	}

	s.logger.Infof("✅ JetStream stream '%s' configured", s.config.StreamName)
	return nil
}

// PublishTranscriptionJob publishes a transcription job to the queue
func (s *NATSService) PublishTranscriptionJob(ctx context.Context, job *TranscriptionJobMessage) error {
	subject := "transcription.job.new"
	
	data, err := json.Marshal(job)
	if err != nil {
		return fmt.Errorf("failed to marshal job: %w", err)
	}

	msg := &nats.Msg{
		Subject: subject,
		Data:    data,
		Header: nats.Header{
			"job_id":      []string{job.JobID},
			"email_source": []string{job.EmailSource},
			"priority":    []string{fmt.Sprintf("%d", job.Priority)},
		},
	}

	_, err = s.js.PublishMsg(ctx, msg)
	if err != nil {
		return fmt.Errorf("failed to publish transcription job: %w", err)
	}

	s.logger.Infof("📤 Published transcription job: %s", job.JobID)
	return nil
}

// PublishTranscriptionResult publishes transcription results
func (s *NATSService) PublishTranscriptionResult(ctx context.Context, result *TranscriptionResultMessage) error {
	subject := "transcription.result.completed"
	
	data, err := json.Marshal(result)
	if err != nil {
		return fmt.Errorf("failed to marshal result: %w", err)
	}

	msg := &nats.Msg{
		Subject: subject,
		Data:    data,
		Header: nats.Header{
			"job_id":    []string{result.JobID},
			"success":   []string{fmt.Sprintf("%t", result.Success)},
			"confidence": []string{fmt.Sprintf("%.2f", result.Confidence)},
		},
	}

	_, err = s.js.PublishMsg(ctx, msg)
	if err != nil {
		return fmt.Errorf("failed to publish transcription result: %w", err)
	}

	s.logger.Infof("📤 Published transcription result: %s", result.JobID)
	return nil
}

// RegisterHandler registers a message handler for a specific message type
func (s *NATSService) RegisterHandler(handler MessageHandler) error {
	s.mu.Lock()
	defer s.mu.Unlock()

	msgType := handler.MessageType()
	s.handlers[msgType] = handler

	s.logger.Infof("✅ Registered handler for message type: %s", msgType)
	return nil
}

// StartConsumers starts all message consumers
func (s *NATSService) StartConsumers() error {
	s.mu.Lock()
	defer s.mu.Unlock()

	// Start transcription job consumer
	if err := s.startTranscriptionJobConsumer(); err != nil {
		return fmt.Errorf("failed to start transcription job consumer: %w", err)
	}

	// Start result consumer
	if err := s.startResultConsumer(); err != nil {
		return fmt.Errorf("failed to start result consumer: %w", err)
	}

	s.logger.Info("✅ All NATS consumers started")
	return nil
}

// startTranscriptionJobConsumer starts the transcription job consumer
func (s *NATSService) startTranscriptionJobConsumer() error {
	consumerConfig := jetstream.ConsumerConfig{
		Name:          "transcription-job-worker",
		Description:   "Processes transcription jobs",
		FilterSubject: "transcription.job.>",
		AckPolicy:     jetstream.AckExplicitPolicy,
		AckWait:       s.config.AckWait,
		MaxAckPending: s.config.MaxAckPending,
		MaxDeliver:    3,
		BackOff:       []time.Duration{1 * time.Second, 5 * time.Second, 10 * time.Second},
	}

	consumer, err := s.js.CreateOrUpdateConsumer(s.ctx, s.config.StreamName, consumerConfig)
	if err != nil {
		return fmt.Errorf("failed to create transcription job consumer: %w", err)
	}

	s.consumers["transcription-job"] = consumer

	// Start consuming messages
	s.wg.Add(1)
	go s.consumeTranscriptionJobs(consumer)

	return nil
}

// startResultConsumer starts the result consumer
func (s *NATSService) startResultConsumer() error {
	consumerConfig := jetstream.ConsumerConfig{
		Name:          "transcription-result-worker",
		Description:   "Processes transcription results",
		FilterSubject: "transcription.result.>",
		AckPolicy:     jetstream.AckExplicitPolicy,
		AckWait:       s.config.AckWait,
		MaxAckPending: s.config.MaxAckPending,
	}

	consumer, err := s.js.CreateOrUpdateConsumer(s.ctx, s.config.StreamName, consumerConfig)
	if err != nil {
		return fmt.Errorf("failed to create result consumer: %w", err)
	}

	s.consumers["transcription-result"] = consumer

	// Start consuming messages
	s.wg.Add(1)
	go s.consumeTranscriptionResults(consumer)

	return nil
}

// consumeTranscriptionJobs processes transcription job messages
func (s *NATSService) consumeTranscriptionJobs(consumer jetstream.Consumer) {
	defer s.wg.Done()

	ctx, cancel := context.WithCancel(s.ctx)
	defer cancel()

	msgs, err := consumer.Messages(jetstream.PullMaxMessages(10))
	if err != nil {
		s.logger.Errorf("Failed to get messages: %v", err)
		return
	}

	for {
		select {
		case <-ctx.Done():
			return
		default:
			msg, err := msgs.Next()
			if err != nil {
				if err == context.Canceled {
					return
				}
				s.logger.Errorf("Error getting next message: %v", err)
				continue
			}

			if err := s.processTranscriptionJob(ctx, msg); err != nil {
				s.logger.Errorf("Error processing transcription job: %v", err)
				msg.Nak()
			} else {
				msg.Ack()
			}
		}
	}
}

// processTranscriptionJob processes a single transcription job
func (s *NATSService) processTranscriptionJob(ctx context.Context, msg jetstream.Msg) error {
	var job TranscriptionJobMessage
	if err := json.Unmarshal(msg.Data(), &job); err != nil {
		return fmt.Errorf("failed to unmarshal job: %w", err)
	}

	s.logger.Infof("📥 Processing transcription job: %s", job.JobID)

	// Here you would integrate with your existing transcription service
	// For now, we'll just log the processing
	
	return nil
}

// consumeTranscriptionResults processes transcription result messages
func (s *NATSService) consumeTranscriptionResults(consumer jetstream.Consumer) {
	defer s.wg.Done()

	ctx, cancel := context.WithCancel(s.ctx)
	defer cancel()

	msgs, err := consumer.Messages(jetstream.PullMaxMessages(10))
	if err != nil {
		s.logger.Errorf("Failed to get messages: %v", err)
		return
	}

	for {
		select {
		case <-ctx.Done():
			return
		default:
			msg, err := msgs.Next()
			if err != nil {
				if err == context.Canceled {
					return
				}
				s.logger.Errorf("Error getting next message: %v", err)
				continue
			}

			if err := s.processTranscriptionResult(ctx, msg); err != nil {
				s.logger.Errorf("Error processing transcription result: %v", err)
				msg.Nak()
			} else {
				msg.Ack()
			}
		}
	}
}

// processTranscriptionResult processes a single transcription result
func (s *NATSService) processTranscriptionResult(ctx context.Context, msg jetstream.Msg) error {
	var result TranscriptionResultMessage
	if err := json.Unmarshal(msg.Data(), &result); err != nil {
		return fmt.Errorf("failed to unmarshal result: %w", err)
	}

	s.logger.Infof("📥 Processing transcription result: %s", result.JobID)

	// Here you would store results in database, trigger notifications, etc.
	
	return nil
}

// Close gracefully shuts down the NATS service
func (s *NATSService) Close() error {
	s.logger.Info("🔄 Shutting down NATS service...")
	
	s.cancel()
	s.wg.Wait()

	if s.conn != nil {
		s.conn.Close()
	}

	s.logger.Info("✅ NATS service shut down complete")
	return nil
}

// Health returns the health status of the NATS service
func (s *NATSService) Health() map[string]interface{} {
	status := map[string]interface{}{
		"connected": s.conn != nil && s.conn.IsConnected(),
		"status":    "unknown",
	}

	if s.conn != nil {
		if s.conn.IsConnected() {
			status["status"] = "healthy"
		} else {
			status["status"] = "disconnected"
		}
		
		stats := s.conn.Stats()
		status["stats"] = map[string]interface{}{
			"in_msgs":     stats.InMsgs,
			"out_msgs":    stats.OutMsgs,
			"in_bytes":    stats.InBytes,
			"out_bytes":   stats.OutBytes,
			"reconnects":  stats.Reconnects,
		}
	}

	return status
}

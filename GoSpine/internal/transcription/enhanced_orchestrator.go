package transcription

import (
	"context"
	"crypto/rand"
	"encoding/hex"
	"fmt"
	"sync"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"gobackend-hvac-kratos/internal/messaging"
	"go.uber.org/zap"
)

// 🎼 Enhanced Transcription Orchestrator - Event-Driven Architecture
// Integrates NATS message queue with NVIDIA STT for scalable transcription processing

// JobStatus represents the status of a transcription job
type JobStatus string

const (
	JobStatusPending    JobStatus = "pending"
	JobStatusProcessing JobStatus = "processing"
	JobStatusCompleted  JobStatus = "completed"
	JobStatusFailed     JobStatus = "failed"
	JobStatusRetrying   JobStatus = "retrying"
)

// TranscriptionJob represents a transcription job with full lifecycle tracking
type TranscriptionJob struct {
	ID           string                 `json:"id"`
	EmailSource  string                 `json:"email_source"`
	FileName     string                 `json:"file_name"`
	AudioData    []byte                 `json:"audio_data"`
	Status       JobStatus              `json:"status"`
	Priority     int                    `json:"priority"`
	Metadata     map[string]string      `json:"metadata"`
	CreatedAt    time.Time              `json:"created_at"`
	StartedAt    *time.Time             `json:"started_at,omitempty"`
	CompletedAt  *time.Time             `json:"completed_at,omitempty"`
	RetryCount   int                    `json:"retry_count"`
	MaxRetries   int                    `json:"max_retries"`
	Result       *TranscriptionResult   `json:"result,omitempty"`
	Error        string                 `json:"error,omitempty"`
	ProcessingNode string               `json:"processing_node,omitempty"`
}

// TranscriptionResult represents the result of a transcription job
type TranscriptionResult struct {
	Text         string            `json:"text"`
	Confidence   float64           `json:"confidence"`
	Duration     time.Duration     `json:"duration"`
	HVACKeywords []string          `json:"hvac_keywords"`
	Segments     []TranscriptionSegment `json:"segments"`
	Metadata     map[string]string `json:"metadata"`
	ProcessedAt  time.Time         `json:"processed_at"`
	ModelVersion string            `json:"model_version"`
}

// TranscriptionSegment represents a segment of transcribed audio
type TranscriptionSegment struct {
	Text       string    `json:"text"`
	StartTime  float64   `json:"start_time"`
	EndTime    float64   `json:"end_time"`
	Confidence float64   `json:"confidence"`
	Speaker    string    `json:"speaker,omitempty"`
}

// OrchestratorConfig holds configuration for the orchestrator
type OrchestratorConfig struct {
	MaxConcurrentJobs    int           `yaml:"max_concurrent_jobs"`
	DefaultJobTimeout    time.Duration `yaml:"default_job_timeout"`
	MaxRetries          int           `yaml:"max_retries"`
	RetryBackoffBase    time.Duration `yaml:"retry_backoff_base"`
	HealthCheckInterval time.Duration `yaml:"health_check_interval"`
	MetricsEnabled      bool          `yaml:"metrics_enabled"`
	NodeID              string        `yaml:"node_id"`
}

// DefaultOrchestratorConfig returns default configuration
func DefaultOrchestratorConfig() *OrchestratorConfig {
	return &OrchestratorConfig{
		MaxConcurrentJobs:    10,
		DefaultJobTimeout:    15 * time.Minute,
		MaxRetries:          3,
		RetryBackoffBase:    2 * time.Second,
		HealthCheckInterval: 30 * time.Second,
		MetricsEnabled:      true,
		NodeID:              generateNodeID(),
	}
}

// EnhancedTranscriptionOrchestrator manages transcription jobs with event-driven architecture
type EnhancedTranscriptionOrchestrator struct {
	config        *OrchestratorConfig
	natsService   *messaging.NATSService
	sttClient     *NvidiaSTTClient
	logger        *log.Helper
	
	// Job management
	activeJobs    map[string]*TranscriptionJob
	jobQueue      chan *TranscriptionJob
	workers       []*TranscriptionWorker
	
	// Synchronization
	mu            sync.RWMutex
	ctx           context.Context
	cancel        context.CancelFunc
	wg            sync.WaitGroup
	
	// Metrics
	metrics       *OrchestratorMetrics
}

// OrchestratorMetrics tracks orchestrator performance
type OrchestratorMetrics struct {
	TotalJobs       int64     `json:"total_jobs"`
	CompletedJobs   int64     `json:"completed_jobs"`
	FailedJobs      int64     `json:"failed_jobs"`
	ActiveJobs      int64     `json:"active_jobs"`
	AverageLatency  float64   `json:"average_latency_ms"`
	ThroughputPerMin float64  `json:"throughput_per_min"`
	LastUpdated     time.Time `json:"last_updated"`
}

// TranscriptionWorker processes individual transcription jobs
type TranscriptionWorker struct {
	id          string
	orchestrator *EnhancedTranscriptionOrchestrator
	logger      *log.Helper
	ctx         context.Context
	cancel      context.CancelFunc
}

// NewEnhancedTranscriptionOrchestrator creates a new enhanced orchestrator
func NewEnhancedTranscriptionOrchestrator(
	config *OrchestratorConfig,
	natsService *messaging.NATSService,
	sttClient *NvidiaSTTClient,
	logger log.Logger,
) (*EnhancedTranscriptionOrchestrator, error) {
	if config == nil {
		config = DefaultOrchestratorConfig()
	}

	ctx, cancel := context.WithCancel(context.Background())

	orchestrator := &EnhancedTranscriptionOrchestrator{
		config:      config,
		natsService: natsService,
		sttClient:   sttClient,
		logger:      log.NewHelper(logger),
		activeJobs:  make(map[string]*TranscriptionJob),
		jobQueue:    make(chan *TranscriptionJob, config.MaxConcurrentJobs*2),
		ctx:         ctx,
		cancel:      cancel,
		metrics:     &OrchestratorMetrics{LastUpdated: time.Now()},
	}

	// Initialize workers
	orchestrator.workers = make([]*TranscriptionWorker, config.MaxConcurrentJobs)
	for i := 0; i < config.MaxConcurrentJobs; i++ {
		worker := &TranscriptionWorker{
			id:           fmt.Sprintf("worker-%d", i),
			orchestrator: orchestrator,
			logger:       log.NewHelper(logger),
		}
		orchestrator.workers[i] = worker
	}

	return orchestrator, nil
}

// Start starts the orchestrator and all workers
func (o *EnhancedTranscriptionOrchestrator) Start() error {
	o.logger.Info("🚀 Starting Enhanced Transcription Orchestrator")

	// Start workers
	for _, worker := range o.workers {
		o.wg.Add(1)
		go worker.start()
	}

	// Start metrics collection
	if o.config.MetricsEnabled {
		o.wg.Add(1)
		go o.collectMetrics()
	}

	// Start health monitoring
	o.wg.Add(1)
	go o.healthMonitor()

	o.logger.Infof("✅ Orchestrator started with %d workers", len(o.workers))
	return nil
}

// SubmitJob submits a new transcription job
func (o *EnhancedTranscriptionOrchestrator) SubmitJob(ctx context.Context, emailSource, fileName string, audioData []byte, metadata map[string]string) (*TranscriptionJob, error) {
	job := &TranscriptionJob{
		ID:          generateJobID(),
		EmailSource: emailSource,
		FileName:    fileName,
		AudioData:   audioData,
		Status:      JobStatusPending,
		Priority:    1, // Default priority
		Metadata:    metadata,
		CreatedAt:   time.Now(),
		MaxRetries:  o.config.MaxRetries,
	}

	// Store job
	o.mu.Lock()
	o.activeJobs[job.ID] = job
	o.mu.Unlock()

	// Publish to NATS
	natsJob := &messaging.TranscriptionJobMessage{
		JobID:       job.ID,
		EmailSource: job.EmailSource,
		FileName:    job.FileName,
		AudioData:   job.AudioData,
		Metadata:    job.Metadata,
		Priority:    job.Priority,
		CreatedAt:   job.CreatedAt,
		RetryCount:  job.RetryCount,
	}

	if err := o.natsService.PublishTranscriptionJob(ctx, natsJob); err != nil {
		o.mu.Lock()
		delete(o.activeJobs, job.ID)
		o.mu.Unlock()
		return nil, fmt.Errorf("failed to publish job to NATS: %w", err)
	}

	// Queue for local processing
	select {
	case o.jobQueue <- job:
		o.logger.Infof("📤 Job queued: %s", job.ID)
	case <-ctx.Done():
		return nil, ctx.Err()
	default:
		o.logger.Warnf("⚠️ Job queue full, job will be processed by NATS consumers: %s", job.ID)
	}

	o.metrics.TotalJobs++
	return job, nil
}

// GetJobStatus returns the status of a job
func (o *EnhancedTranscriptionOrchestrator) GetJobStatus(jobID string) (*TranscriptionJob, error) {
	o.mu.RLock()
	defer o.mu.RUnlock()

	job, exists := o.activeJobs[jobID]
	if !exists {
		return nil, fmt.Errorf("job not found: %s", jobID)
	}

	return job, nil
}

// start starts a worker
func (w *TranscriptionWorker) start() {
	defer w.orchestrator.wg.Done()

	w.ctx, w.cancel = context.WithCancel(w.orchestrator.ctx)
	defer w.cancel()

	w.logger.Infof("🔄 Worker %s started", w.id)

	for {
		select {
		case <-w.ctx.Done():
			w.logger.Infof("🛑 Worker %s stopped", w.id)
			return
		case job := <-w.orchestrator.jobQueue:
			w.processJob(job)
		}
	}
}

// processJob processes a single transcription job
func (w *TranscriptionWorker) processJob(job *TranscriptionJob) {
	startTime := time.Now()
	w.logger.Infof("🎤 Worker %s processing job: %s", w.id, job.ID)

	// Update job status
	w.orchestrator.mu.Lock()
	job.Status = JobStatusProcessing
	job.ProcessingNode = w.orchestrator.config.NodeID
	now := time.Now()
	job.StartedAt = &now
	w.orchestrator.mu.Unlock()

	// Create context with timeout
	ctx, cancel := context.WithTimeout(w.ctx, w.orchestrator.config.DefaultJobTimeout)
	defer cancel()

	// Process transcription
	result, err := w.orchestrator.sttClient.TranscribeAudioData(ctx, job.AudioData, job.FileName, job.EmailSource)
	if err != nil {
		w.handleJobError(job, err)
		return
	}

	// Convert result
	transcriptionResult := &TranscriptionResult{
		Text:         result.Transcript,
		Confidence:   result.Confidence,
		Duration:     time.Since(startTime),
		HVACKeywords: []string{}, // Extract from result.Analysis if available
		Metadata:     map[string]string{}, // Convert from result.Analysis if needed
		ProcessedAt:  time.Now(),
		ModelVersion: "nvidia-nemo-polish",
	}

	// Update job with result
	w.orchestrator.mu.Lock()
	job.Status = JobStatusCompleted
	job.Result = transcriptionResult
	completedAt := time.Now()
	job.CompletedAt = &completedAt
	w.orchestrator.mu.Unlock()

	// Publish result to NATS
	natsResult := &messaging.TranscriptionResultMessage{
		JobID:        job.ID,
		Text:         transcriptionResult.Text,
		Confidence:   transcriptionResult.Confidence,
		Duration:     transcriptionResult.Duration,
		HVACKeywords: transcriptionResult.HVACKeywords,
		Metadata:     transcriptionResult.Metadata,
		ProcessedAt:  transcriptionResult.ProcessedAt,
		Success:      true,
	}

	if err := w.orchestrator.natsService.PublishTranscriptionResult(ctx, natsResult); err != nil {
		w.logger.Errorf("❌ Failed to publish result to NATS: %v", err)
	}

	w.orchestrator.metrics.CompletedJobs++
	w.logger.Infof("✅ Worker %s completed job: %s (%.2fs)", w.id, job.ID, transcriptionResult.Duration.Seconds())
}

// handleJobError handles job processing errors
func (w *TranscriptionWorker) handleJobError(job *TranscriptionJob, err error) {
	w.logger.Errorf("❌ Worker %s job failed: %s - %v", w.id, job.ID, err)

	w.orchestrator.mu.Lock()
	defer w.orchestrator.mu.Unlock()

	job.RetryCount++
	job.Error = err.Error()

	if job.RetryCount < job.MaxRetries {
		// Schedule retry
		job.Status = JobStatusRetrying
		w.logger.Infof("🔄 Scheduling retry %d/%d for job: %s", job.RetryCount, job.MaxRetries, job.ID)
		
		// Add exponential backoff
		backoff := time.Duration(job.RetryCount) * w.orchestrator.config.RetryBackoffBase
		time.AfterFunc(backoff, func() {
			select {
			case w.orchestrator.jobQueue <- job:
			default:
				w.logger.Warnf("⚠️ Failed to requeue job for retry: %s", job.ID)
			}
		})
	} else {
		// Mark as failed
		job.Status = JobStatusFailed
		failedAt := time.Now()
		job.CompletedAt = &failedAt
		w.orchestrator.metrics.FailedJobs++
	}
}

// collectMetrics collects and updates orchestrator metrics
func (o *EnhancedTranscriptionOrchestrator) collectMetrics() {
	defer o.wg.Done()

	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-o.ctx.Done():
			return
		case <-ticker.C:
			o.updateMetrics()
		}
	}
}

// updateMetrics updates the orchestrator metrics
func (o *EnhancedTranscriptionOrchestrator) updateMetrics() {
	o.mu.RLock()
	activeCount := int64(len(o.activeJobs))
	o.mu.RUnlock()

	o.metrics.ActiveJobs = activeCount
	o.metrics.LastUpdated = time.Now()

	// Calculate throughput (simplified)
	if o.metrics.TotalJobs > 0 {
		o.metrics.ThroughputPerMin = float64(o.metrics.CompletedJobs) / time.Since(o.metrics.LastUpdated).Minutes()
	}
}

// healthMonitor monitors the health of the orchestrator
func (o *EnhancedTranscriptionOrchestrator) healthMonitor() {
	defer o.wg.Done()

	ticker := time.NewTicker(o.config.HealthCheckInterval)
	defer ticker.Stop()

	for {
		select {
		case <-o.ctx.Done():
			return
		case <-ticker.C:
			o.performHealthCheck()
		}
	}
}

// performHealthCheck performs a health check
func (o *EnhancedTranscriptionOrchestrator) performHealthCheck() {
	// Check NATS health
	natsHealth := o.natsService.Health()
	if !natsHealth["connected"].(bool) {
		o.logger.Warn("⚠️ NATS connection unhealthy")
	}

	// Check worker health
	activeWorkers := 0
	for _, worker := range o.workers {
		if worker.ctx != nil && worker.ctx.Err() == nil {
			activeWorkers++
		}
	}

	if activeWorkers < len(o.workers) {
		o.logger.Warnf("⚠️ Only %d/%d workers active", activeWorkers, len(o.workers))
	}
}

// GetMetrics returns current orchestrator metrics
func (o *EnhancedTranscriptionOrchestrator) GetMetrics() *OrchestratorMetrics {
	o.updateMetrics()
	return o.metrics
}

// Stop gracefully stops the orchestrator
func (o *EnhancedTranscriptionOrchestrator) Stop() error {
	o.logger.Info("🔄 Stopping Enhanced Transcription Orchestrator")
	
	o.cancel()
	o.wg.Wait()
	close(o.jobQueue)

	o.logger.Info("✅ Orchestrator stopped")
	return nil
}

// generateJobID generates a unique job ID
func generateJobID() string {
	bytes := make([]byte, 8)
	rand.Read(bytes)
	return fmt.Sprintf("job_%s_%d", hex.EncodeToString(bytes), time.Now().Unix())
}

// generateNodeID generates a unique node ID
func generateNodeID() string {
	bytes := make([]byte, 4)
	rand.Read(bytes)
	return fmt.Sprintf("node_%s", hex.EncodeToString(bytes))
}
